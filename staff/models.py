from django.db import models

from core.generate_resource_id import generate_staff_id
from core.model import BaseModel
from staff.enums import Department, Gender, Position, Region


# 员工模型
class Staff(BaseModel):
    # 员工姓名
    name = models.CharField(max_length=50,verbose_name='员工姓名')
    # 员工性别
    gender = models.CharField(max_length=50,choices=Gender.choices,verbose_name='员工性别')
    # 员工电话
    phone = models.CharField(max_length=50,verbose_name='员工电话')
    # 员工邮箱
    email = models.EmailField(max_length=255,verbose_name='员工邮箱')
    # 员工部门
    department = models.CharField(max_length=50,choices=Department.choices,verbose_name='员工部门')
    # 员工职位
    position = models.CharField(max_length=50,choices=Position.choices,verbose_name='员工职位')
    # 员工区域
    region = models.CharField(max_length=50,choices=Region.choices,verbose_name='员工区域')
    # 入职日期
    join_date = models.DateField(verbose_name='入职日期')
    # 合同到期日期
    contract_end_date = models.DateField(verbose_name='合同到期日期')
    # 备注
    remark = models.TextField(verbose_name='备注')
    # 密码
    password = models.CharField(max_length=128, verbose_name="密码",blank=True, default='')
    # 员工id
    uid = models.CharField(max_length=50,verbose_name='员工id',unique=True,blank=True,default=generate_staff_id)
    # 是否启用
    is_active = models.BooleanField(default=True,verbose_name='是否启用',blank=True)
    
    class Meta:
        verbose_name = '员工管理'
        verbose_name_plural = '员工管理'
    
    def __str__(self):
        return self.name
    
    # 检查电话号码是否已存在
    @classmethod
    def check_phone_exist(cls, phone):
        return cls.objects.filter(phone=phone).exists()

    # 检查邮箱是否已存在
    @classmethod
    def check_email_exist(cls, email):
        return cls.objects.filter(email=email).exists()
    