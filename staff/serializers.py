from rest_framework import serializers
from staff.models import Staff


# 员工列表序列化器
class StaffListSerializer(serializers.ModelSerializer):
    
    # 性别显示
    gender_display = serializers.SerializerMethodField()
    # 部门显示
    department_display = serializers.SerializerMethodField()
    # 职位显示
    position_display = serializers.SerializerMethodField()
    # 区域显示
    region_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Staff
        fields = ['uid', 'name', 'gender_display', 'phone', 'department_display', 'position_display', 'region_display', 'join_date', 'remark']
        
    
    def get_gender_display(self, obj):
        return obj.get_gender_display()
    
    def get_department_display(self, obj):
        return obj.get_department_display()
    
    def get_position_display(self, obj):
        return obj.get_position_display()
    
    def get_region_display(self, obj):
        return obj.get_region_display()