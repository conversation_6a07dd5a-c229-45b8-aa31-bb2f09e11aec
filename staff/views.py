from django.shortcuts import render

from core.view import PaginationListBaseView
from staff.enums import Department, Gender, Position, Region
from staff.models import Staff
from staff.serializers import StaffListSerializer


# 员工列表
class StaffListView(PaginationListBaseView):
    authentication_classes = []
    permission_classes = []
    staff_required_permission = None
    
    serializer_class = StaffListSerializer
    response_msg = "Success"
    error_response_msg = "Failed"
    search_fields = ['name', 'phone', 'email', 'department', 'position', 'region']
    additional_data = None
    audit_log_message = ""
    
    def get_queryset(self):
        base_queryset = Staff.objects.filter().order_by('-join_date')
        
        department = self.request.query_params.get('department', None)
        position = self.request.query_params.get('position', None)
        region = self.request.query_params.get('region', None)
        gender = self.request.query_params.get('gender', None)
        status = self.request.query_params.get('status', True)
        
        if department and department in Department.values:
            base_queryset = base_queryset.filter(department=department)
            
        if position and position in Position.values:
            base_queryset = base_queryset.filter(position=position)
            
        if region and region in Region.values:
            base_queryset = base_queryset.filter(region=region)
        
        if gender and gender in Gender.values:
            base_queryset = base_queryset.filter(gender=gender)
        
        return base_queryset